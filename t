// MARK: - Core Data Models (EinkaufsItem+CoreDataClass.swift)
import Foundation
import CoreData
import SwiftUI

@objc(EinkaufsItem)
public class EinkaufsItem: NSManagedObject, Identifiable {
    
    public var wrappedName: String {
        name ?? "Unbekannter Artikel"
    }
    
    public var wrappedCategory: String {
        category ?? "Sonstiges"
    }
    
    public var wrappedNotes: String {
        notes ?? ""
    }
    
    public var priorityEnum: Priority {
        get { Priority(rawValue: priority) ?? .normal }
        set { priority = newValue.rawValue }
    }
    
    public var categoryEnum: ShoppingCategory {
        get { ShoppingCategory(rawValue: wrappedCategory) ?? .other }
        set { category = newValue.rawValue }
    }
    
    public var formattedPrice: String {
        if estimatedPrice > 0 {
            return String(format: "%.2f €", estimatedPrice)
        }
        return ""
    }
    
    public var formattedQuantity: String {
        return quantity > 1 ? "\(quantity)x" : ""
    }
}

// MARK: - Core Data Properties Extension
extension EinkaufsItem {
    @nonobjc public class func fetchRequest() -> NSFetchRequest<EinkaufsItem> {
        return NSFetchRequest<EinkaufsItem>(entityName: "EinkaufsItem")
    }
    
    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var category: String?
    @NSManaged public var isPurchased: Bool
    @NSManaged public var quantity: Int32
    @NSManaged public var priority: String
    @NSManaged public var notes: String?
    @NSManaged public var estimatedPrice: Double
    @NSManaged public var dateAdded: Date?
    @NSManaged public var dateCompleted: Date?
    @NSManaged public var actualPrice: Double
    @NSManaged public var isImportant: Bool
    @NSManaged public var reminderDate: Date?
    @NSManaged public var store: String?
    
    public override func awakeFromInsert() {
        super.awakeFromInsert()
        id = UUID()
        dateAdded = Date()
        priority = Priority.normal.rawValue
        quantity = 1
    }
}

// MARK: - Enums
enum Priority: String, CaseIterable, Codable {
    case low = "Niedrig"
    case normal = "Normal"
    case high = "Hoch"
    case urgent = "Dringend"
    
    var color: Color {
        switch self {
        case .low: return .green
        case .normal: return .blue
        case .high: return .orange
        case .urgent: return .red
        }
    }
    
    var icon: String {
        switch self {
        case .low: return "arrow.down.circle.fill"
        case .normal: return "minus.circle.fill"
        case .high: return "arrow.up.circle.fill"
        case .urgent: return "exclamationmark.triangle.fill"
        }
    }
    
    var sortOrder: Int {
        switch self {
        case .urgent: return 0
        case .high: return 1
        case .normal: return 2
        case .low: return 3
        }
    }
}

enum ShoppingCategory: String, CaseIterable, Codable {
    case fruits = "Obst"
    case vegetables = "Gemüse"
    case beverages = "Getränke"
    case bakery = "Backwaren"
    case household = "Haushalt"
    case dairy = "Milchprodukte"
    case meat = "Fleisch & Fisch"
    case frozen = "Tiefkühl"
    case snacks = "Snacks"
    case personal = "Körperpflege"
    case cleaning = "Reinigung"
    case pharmacy = "Apotheke"
    case electronics = "Elektronik"
    case clothing = "Kleidung"
    case other = "Sonstiges"
    
    var icon: String {
        switch self {
        case .fruits: return "apple.fill"
        case .vegetables: return "carrot.fill"
        case .beverages: return "wineglass.fill"
        case .bakery: return "croissant.fill"
        case .household: return "house.fill"
        case .dairy: return "drop.fill"
        case .meat: return "fish.fill"
        case .frozen: return "snowflake"
        case .snacks: return "bag.fill"
        case .personal: return "person.fill"
        case .cleaning: return "sparkles"
        case .pharmacy: return "cross.fill"
        case .electronics: return "desktopcomputer"
        case .clothing: return "tshirt.fill"
        case .other: return "tag.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .fruits: return .red
        case .vegetables: return .green
        case .beverages: return .blue
        case .bakery: return .orange
        case .household: return .brown
        case .dairy: return .cyan
        case .meat: return .pink
        case .frozen: return .mint
        case .snacks: return .yellow
        case .personal: return .purple
        case .cleaning: return .indigo
        case .pharmacy: return .red
        case .electronics: return .gray
        case .clothing: return .pink
        case .other: return .secondary
        }
    }
    
    var gradient: LinearGradient {
        LinearGradient(
            colors: [color.opacity(0.8), color.opacity(0.3)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

// MARK: - Enhanced Core Data ViewModel
@MainActor
class ShoppingListViewModel: ObservableObject {
    private let container: NSPersistentContainer
    
    @Published var searchText = ""
    @Published var selectedCategory: ShoppingCategory?
    @Published var sortOption: SortOption = .priority
    @Published var showCompleted = true
    @Published var filterPriority: Priority?
    
    // Form states
    @Published var showingAddSheet = false
    @Published var showingSettings = false
    @Published var showingStatistics = false
    
    // Statistics
    @Published var statistics = ShoppingStatistics()
    
    enum SortOption: String, CaseIterable {
        case priority = "Priorität"
        case dateAdded = "Hinzugefügt"
        case name = "Name"
        case category = "Kategorie"
        case price = "Preis"
        
        var icon: String {
            switch self {
            case .priority: return "flag.fill"
            case .dateAdded: return "calendar"
            case .name: return "textformat"
            case .category: return "folder"
            case .price: return "eurosign.circle"
            }
        }
    }
    
    struct ShoppingStatistics {
        var totalItems: Int = 0
        var completedItems: Int = 0
        var totalEstimatedCost: Double = 0
        var totalActualCost: Double = 0
        var completionRate: Double = 0
        var averageItemPrice: Double = 0
        var topCategory: String = ""
        var urgentItemsCount: Int = 0
        
        var formattedEstimatedCost: String {
            String(format: "%.2f €", totalEstimatedCost)
        }
        
        var formattedActualCost: String {
            String(format: "%.2f €", totalActualCost)
        }
        
        var savings: Double {
            totalEstimatedCost - totalActualCost
        }
        
        var formattedSavings: String {
            let amount = abs(savings)
            let prefix = savings >= 0 ? "Gespart: " : "Über Budget: "
            return prefix + String(format: "%.2f €", amount)
        }
    }
    
    init(container: NSPersistentContainer) {
        self.container = container
    }
    
    // MARK: - CRUD Operations
    func addItem(name: String, category: ShoppingCategory, quantity: Int32 = 1, 
                priority: Priority = .normal, notes: String = "", 
                estimatedPrice: Double = 0, store: String = "") {
        
        let context = container.viewContext
        let item = EinkaufsItem(context: context)
        
        item.name = name.trimmingCharacters(in: .whitespacesAndNewlines)
        item.categoryEnum = category
        item.quantity = quantity
        item.priorityEnum = priority
        item.notes = notes.trimmingCharacters(in: .whitespacesAndNewlines)
        item.estimatedPrice = estimatedPrice
        item.store = store.trimmingCharacters(in: .whitespacesAndNewlines)
        
        saveContext()
        updateStatistics()
        
        // Haptic feedback
        let impact = UIImpactFeedbackGenerator(style: .medium)
        impact.impactOccurred()
    }
    
    func togglePurchased(_ item: EinkaufsItem) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            item.isPurchased.toggle()
            item.dateCompleted = item.isPurchased ? Date() : nil
        }
        
        saveContext()
        updateStatistics()
        
        // Haptic feedback
        let style: UIImpactFeedbackGenerator.FeedbackStyle = item.isPurchased ? .light : .medium
        let impact = UIImpactFeedbackGenerator(style: style)
        impact.impactOccurred()
    }
    
    func updateItem(_ item: EinkaufsItem, actualPrice: Double) {
        item.actualPrice = actualPrice
        saveContext()
        updateStatistics()
    }
    
    func deleteItem(_ item: EinkaufsItem) {
        let context = container.viewContext
        
        withAnimation(.easeInOut(duration: 0.3)) {
            context.delete(item)
        }
        
        saveContext()
        updateStatistics()
    }
    
    func duplicateItem(_ item: EinkaufsItem) {
        let context = container.viewContext
        
        let newItem = EinkaufsItem(context: context)
        newItem.name = item.name
        newItem.category = item.category
        newItem.quantity = item.quantity
        newItem.priority = item.priority
        newItem.notes = item.notes
        newItem.estimatedPrice = item.estimatedPrice
        newItem.store = item.store
        newItem.isPurchased = false
        newItem.dateCompleted = nil
        
        saveContext()
        updateStatistics()
    }
    
    func clearCompleted() {
        let context = container.viewContext
        let request: NSFetchRequest<EinkaufsItem> = EinkaufsItem.fetchRequest()
        request.predicate = NSPredicate(format: "isPurchased == true")
        
        do {
            let completedItems = try context.fetch(request)
            
            withAnimation(.easeInOut(duration: 0.5)) {
                completedItems.forEach { context.delete($0) }
            }
            
            saveContext()
            updateStatistics()
        } catch {
            print("Error clearing completed items: \(error)")
        }
    }
    
    func markAllAsCompleted(for category: ShoppingCategory) {
        let context = container.viewContext
        let request: NSFetchRequest<EinkaufsItem> = EinkaufsItem.fetchRequest()
        request.predicate = NSPredicate(format: "category == %@ AND isPurchased == false", category.rawValue)
        
        do {
            let items = try context.fetch(request)
            
            withAnimation(.spring()) {
                items.forEach { item in
                    item.isPurchased = true
                    item.dateCompleted = Date()
                }
            }
            
            saveContext()
            updateStatistics()
        } catch {
            print("Error marking all as completed: \(error)")
        }
    }
    
    private func saveContext() {
        let context = container.viewContext
        
        if context.hasChanges {
            try? context.save()
        }
    }
    
    private func updateStatistics() {
        let context = container.viewContext
        let request: NSFetchRequest<EinkaufsItem> = EinkaufsItem.fetchRequest()
        
        do {
            let allItems = try context.fetch(request)
            
            statistics.totalItems = allItems.count
            statistics.completedItems = allItems.filter { $0.isPurchased }.count
            statistics.totalEstimatedCost = allItems.reduce(0) { $0 + $1.estimatedPrice }
            statistics.totalActualCost = allItems.filter { $0.isPurchased }.reduce(0) { $0 + $1.actualPrice }
            statistics.completionRate = allItems.isEmpty ? 0 : Double(statistics.completedItems) / Double(statistics.totalItems)
            statistics.urgentItemsCount = allItems.filter { $0.priorityEnum == .urgent }.count
            
            // Calculate average price
            let itemsWithPrice = allItems.filter { $0.estimatedPrice > 0 }
            statistics.averageItemPrice = itemsWithPrice.isEmpty ? 0 : statistics.totalEstimatedCost / Double(itemsWithPrice.count)
            
            // Find top category
            let categoryGroups = Dictionary(grouping: allItems, by: { $0.wrappedCategory })
            let topCategoryPair = categoryGroups.max { $0.value.count < $1.value.count }
            statistics.topCategory = topCategoryPair?.key ?? ""
            
        } catch {
            print("Error updating statistics: \(error)")
        }
    }
}

// MARK: - Main ContentView
struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var viewModel: ShoppingListViewModel
    
    init() {
        let container = PersistenceController.shared.container
        _viewModel = StateObject(wrappedValue: ShoppingListViewModel(container: container))
    }
    
    var body: some View {
        NavigationSplitView {
            CategorySidebar(viewModel: viewModel)
        } detail: {
            ShoppingListDetailView(viewModel: viewModel)
        }
        .sheet(isPresented: $viewModel.showingAddSheet) {
            AddItemSheet(viewModel: viewModel)
        }
        .sheet(isPresented: $viewModel.showingSettings) {
            SettingsView(viewModel: viewModel)
        }
        .sheet(isPresented: $viewModel.showingStatistics) {
            StatisticsView(viewModel: viewModel)
        }
    }
}

// MARK: - Category Sidebar
struct CategorySidebar: View {
    @ObservedObject var viewModel: ShoppingListViewModel
    @FetchRequest(sortDescriptors: []) private var allItems: FetchedResults<EinkaufsItem>
    
    var body: some View {
        List(selection: $viewModel.selectedCategory) {
            Section("Übersicht") {
                NavigationLink {
                    AllItemsView(viewModel: viewModel)
                } label: {
                    HStack {
                        Image(systemName: "list.bullet.circle.fill")
                            .foregroundColor(.blue)
                            .font(.title2)
                        
                        VStack(alignment: .leading) {
                            Text("Alle Artikel")
                                .font(.headline)
                            Text("\(allItems.count) Artikel")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if viewModel.statistics.urgentItemsCount > 0 {
                            Text("\(viewModel.statistics.urgentItemsCount)")
                                .font(.caption.weight(.bold))
                                .foregroundColor(.white)
                                .padding(6)
                                .background(Color.red)
                                .clipShape(Circle())
                        }
                    }
                }
            }
            
            Section("Kategorien") {
                ForEach(ShoppingCategory.allCases, id: \.self) { category in
                    CategoryRow(
                        category: category,
                        items: allItems.filter { $0.categoryEnum == category },
                        isSelected: viewModel.selectedCategory == category
                    )
                    .tag(category)
                }
            }
            
            Section("Aktionen") {
                Button {
                    viewModel.clearCompleted()
                } label: {
                    Label("Erledigte löschen", systemImage: "trash.circle.fill")
                        .foregroundColor(.red)
                }
                .disabled(allItems.filter { $0.isPurchased }.isEmpty)
                
                Button {
                    viewModel.showingStatistics = true
                } label: {
                    Label("Statistiken", systemImage: "chart.bar.fill")
                        .foregroundColor(.green)
                }
            }
        }
        .navigationTitle("EinkaufsApp Pro")
        .listStyle(.sidebar)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button {
                    viewModel.showingSettings = true
                } label: {
                    Image(systemName: "gearshape.fill")
                }
            }
        }
        .onAppear {
            viewModel.updateStatistics()
        }
    }
}

struct CategoryRow: View {
    let category: ShoppingCategory
    let items: [EinkaufsItem]
    let isSelected: Bool
    
    private var completedCount: Int {
        items.filter { $0.isPurchased }.count
    }
    
    private var urgentCount: Int {
        items.filter { $0.priorityEnum == .urgent && !$0.isPurchased }.count
    }
    
    var body: some View {
        HStack {
            // Category Icon
            RoundedRectangle(cornerRadius: 10)
                .fill(category.gradient)
                .frame(width: 40, height: 40)
                .overlay {
                    Image(systemName: category.icon)
                        .foregroundColor(.white)
                        .font(.system(size: 18, weight: .semibold))
                }
                .shadow(color: category.color.opacity(0.3), radius: 4, x: 0, y: 2)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(category.rawValue)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    if urgentCount > 0 {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                }
                
                if !items.isEmpty {
                    HStack(spacing: 8) {
                        Text("\(completedCount)/\(items.count)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        ProgressView(value: Double(completedCount), total: Double(items.count))
                            .progressViewStyle(LinearProgressViewStyle(tint: category.color))
                            .frame(height: 4)
                    }
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                if !items.isEmpty {
                    Text("\(items.count)")
                        .font(.headline.weight(.semibold))
                        .foregroundColor(category.color)
                }
                
                if urgentCount > 0 {
                    Text("⚠️ \(urgentCount)")
                        .font(.caption2.weight(.bold))
                        .foregroundColor(.red)
                }
            }
        }
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? category.color.opacity(0.15) : Color.clear)
                .animation(.easeInOut(duration: 0.2), value: isSelected)
        )
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isSelected)
    }
}

// MARK: - Shopping List Detail View
struct ShoppingListDetailView: View {
    @ObservedObject var viewModel: ShoppingListViewModel
    @FetchRequest private var items: FetchedResults<EinkaufsItem>
    
    init(viewModel: ShoppingListViewModel) {
        self.viewModel = viewModel
        
        // Dynamic fetch request based on selected category
        let request: NSFetchRequest<EinkaufsItem> = EinkaufsItem.fetchRequest()
        
        if let category = viewModel.selectedCategory {
            request.predicate = NSPredicate(format: "category == %@", category.rawValue)
        }
        
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \EinkaufsItem.isPurchased, ascending: true),
            NSSortDescriptor(keyPath: \EinkaufsItem.priority, ascending: true),
            NSSortDescriptor(keyPath: \EinkaufsItem.dateAdded, ascending: false)
        ]
        
        _items = FetchRequest(fetchRequest: request)
    }
    
    var filteredItems: [EinkaufsItem] {
        var filtered = Array(items)
        
        // Apply search filter
        if !viewModel.searchText.isEmpty {
            filtered = filtered.filter { item in
                item.wrappedName.localizedCaseInsensitiveContains(viewModel.searchText) ||
                item.wrappedNotes.localizedCaseInsensitiveContains(viewModel.searchText) ||
                (item.store ?? "").localizedCaseInsensitiveContains(viewModel.searchText)
            }
        }
        
        // Apply completed filter
        if !viewModel.showCompleted {
            filtered = filtered.filter { !$0.isPurchased }
        }
        
        // Apply priority filter
        if let priority = viewModel.filterPriority {
            filtered = filtered.filter { $0.priorityEnum == priority }
        }
        
        return filtered
    }
    
    var body: some View {
        VStack(spacing: 0) {
            if let category = viewModel.selectedCategory {
                // Header
                CategoryHeaderView(
                    category: category,
                    items: filteredItems,
                    viewModel: viewModel
                )
                
                Divider()
                
                // Search and Filter Bar
                SearchAndFilterView(viewModel: viewModel)
                    .padding()
                    .background(.ultraThinMaterial)
                
                Divider()
                
                // Items List
                if filteredItems.isEmpty {
                    EmptyStateView(
                        category: category,
                        hasSearchText: !viewModel.searchText.isEmpty
                    )
                } else {
                    ScrollView {
                        LazyVGrid(
                            columns: [
                                GridItem(.flexible(), spacing: 16),
                                GridItem(.flexible(), spacing: 16)
                            ],
                            spacing: 16
                        ) {
                            ForEach(filteredItems, id: \.objectID) { item in
                                ItemCard(item: item, viewModel: viewModel)
                            }
                        }
                        .padding()
                    }
                }
            } else {
                WelcomeView()
            }
        }
        .navigationTitle(viewModel.selectedCategory?.rawValue ?? "EinkaufsApp Pro")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItemGroup(placement: .navigationBarTrailing) {
                if viewModel.selectedCategory != nil {
                    Menu {
                        Button("Alle als erledigt markieren") {
                            if let category = viewModel.selectedCategory {
                                viewModel.markAllAsCompleted(for: category)
                            }
                        }
                        
                        Button("Sortierung: \(viewModel.sortOption.rawValue)") {
                            // Cycle through sort options
                            let options = ShoppingListViewModel.SortOption.allCases
                            if let currentIndex = options.firstIndex(of: viewModel.sortOption) {
                                let nextIndex = (currentIndex + 1) % options.count
                                viewModel.sortOption = options[nextIndex]
                            }
                        }
                        
                        Toggle("Erledigte anzeigen", isOn: $viewModel.showCompleted)
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                    
                    Button {
                        viewModel.showingAddSheet = true
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                    }
                }
            }
        }
        .background(
            LinearGradient(
                colors: [
                    viewModel.selectedCategory?.color.opacity(0.05) ?? Color.clear,
                    Color.clear
                ],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
        )
    }
}

// MARK: - Supporting Views
struct CategoryHeaderView: View {
    let category: ShoppingCategory
    let items: [EinkaufsItem]
    @ObservedObject var viewModel: ShoppingListViewModel
    
    private var completedCount: Int { items.filter { $0.isPurchased }.count }
    private var totalEstimated: Double { items.reduce(0) { $0 + $1.estimatedPrice } }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                RoundedRectangle(cornerRadius: 15)
                    .fill(category.gradient)
                    .frame(width: 60, height: 60)
                    .overlay {
                        Image(systemName: category.icon)
                            .foregroundColor(.white)
                            .font(.system(size: 24, weight: .bold))
                    }
                    .shadow(color: category.color.opacity(0.4), radius: 10, x: 0, y: 5)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(category.rawValue)
                        .font(.title.weight(.bold))
                        .foregroundColor(.primary)
                    
                    Text("\(items.count) Artikel")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(completedCount)/\(items.count)")
                        .font(.title2.weight(.semibold))
                        .foregroundColor(category.color)
                    
                    if totalEstimated > 0 {
                        Text(String(format: "%.2f €", totalEstimated))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            if !items.isEmpty {
                ProgressView(value: Double(completedCount), total: Double(items.count))
                    .progressViewStyle(LinearProgressViewStyle(tint: category.color))
                    .scaleEffect(y: 2)
                    .animation(.easeInOut(duration: 0.3), value: completedCount)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
    }
}

struct SearchAndFilterView: View {
    @ObservedObject var viewModel: ShoppingListViewModel
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Suchen...", text: $viewModel.searchText)
                    .textFieldStyle(.plain)
                
                if !viewModel.searchText.isEmpty {
                    Button {
                        viewModel.searchText = ""
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(12)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    FilterChip(
                        title: "Alle",
                        isSelected: viewModel.filterPriority == nil,
                        action: { viewModel.filterPriority = nil }
                    )
                    
                    ForEach(Priority.allCases, id: \.self) { priority in
                        FilterChip(
                            title: priority.rawValue,
                            isSelected: viewModel.filterPriority == priority,
                            color: priority.color,
                            action: { 
                                viewModel.filterPriority = viewModel.filterPriority == priority ? nil : priority 
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    init(title: String, isSelected: Bool, color: Color = .blue, action: @escaping () -> Void) {
        self.title = title
        self.isSelected = isSelected
        self.color = color
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption.weight(.medium))
                .foregroundColor(isSelected ? .white : color)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? color : color.opacity(0.1))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        }
        .buttonStyle(.plain)
    }
}

struct ItemCard: View {
    let item: EinkaufsItem
    @ObservedObject var viewModel: ShoppingListViewModel
    @State private var showingDetails = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Button {
                    viewModel.togglePurchased(item)
                } label: {
                    Image(systemName: item.isPurchased ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(item.isPurchased ? .green : .gray)
                        .font(.title2)
                        .scaleEffect(item.isPurchased ? 1.1 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: item.isPurchased)
                }
                
                Spacer()
                
                // Priority indicator
                Image(systemName: item.priorityEnum.icon)
                    .foregroundColor(item.priorityEnum.color)
                    .font(.caption)
                
                // Quantity badge
                if item.quantity > 1 {
                    Text("\(item.quantity)x")
                        .font(.caption2.weight(.bold))
                        .foregroundColor(.white)
                        .padding(4)
                        .background(Circle().fill(Color.blue))
                }
            }
            
            VStack(alignment: .leading, spacing: 6) {
                Text(item.wrappedName)
                    .font(.headline)
                    .foregroundColor(item.isPurchased ? .secondary : .primary)
                    .strikethrough(item.isPurchased)
                    .lineLimit(2)
                
                if !item.wrappedNotes.isEmpty {
                    Text(item.wrappedNotes)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                HStack {
                    if item.estimatedPrice > 0 {
                        Text(item.formattedPrice)
                            .font(.caption.weight(.medium))
                            .foregroundColor(.green)
                    }
                    
                    Spacer()
                    
                    if let store = item.store, !store.isEmpty {
                        Text(store)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .padding(4)
                            .background(Color(.systemGray6))
                            .cornerRadius(4)
                    }
                }
            }
        }
        .padding()
        .frame(maxWidth: .infinity, minHeight: 140, alignment: .topLeading)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            item.priorityEnum == .urgent ? Color.red.opacity(0.5) : 
                            item.categoryEnum.color.opacity(0.2), 
                            lineWidth: item.priorityEnum == .urgent ? 2 : 1
                        )
                )
        )
        .scaleEffect(item.isPurchased ? 0.95 : 1.0)
        .opacity(item.isPurchased ? 0.7 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: item.isPurchased)
        .contextMenu {
            Button("Details bearbeiten") {
                showingDetails = true
            }
            
            Button("Duplizieren") {
                viewModel.duplicateItem(item)
            }
            
            Button("Löschen", role: .destructive) {
                viewModel.deleteItem(item)
            }
        }
        .sheet(isPresented: $showingDetails) {
            ItemDetailSheet(item: item, viewModel: viewModel)
        }
    }
}

struct EmptyStateView: View {
    let category: ShoppingCategory
    let hasSearchText: Bool
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: hasSearchText ? "magnifyingglass" : category.icon)
                .font(.system(size: 60))
                .foregroundColor(category.color.opacity(0.6))
            
            VStack(spacing: 8) {
                Text(hasSearchText ? "Keine Ergebnisse" : "Keine Artikel")
                    .font(.title2.weight(.semibold))
                    .foregroundColor(.primary)
                
                Text(hasSearchText ? 
                     "Versuche einen anderen Suchbegriff" : 
                     "Füge deinen ersten Artikel in \(category.rawValue) hinzu")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            if !hasSearchText {
                Button("Artikel hinzufügen") {
                    // This would trigger the add sheet
                }
                .buttonStyle(.borderedProminent)
                .tint(category.color)
            }
        }
        .padding(40)
    }
}

struct WelcomeView: View {
    var body: some View {
        VStack(spacing: 32) {
            VStack(spacing: 16) {
                Image(systemName: "cart.fill")
                    .font(.system(size: 80))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                VStack(spacing: 8) {
                    Text("Willkommen bei EinkaufsApp Pro")
                        .font(.title.weight(.bold))
                        .multilineTextAlignment(.center)
                    
                    Text("Organisiere deine Einkäufe intelligent und effizient")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            VStack(spacing: 16) {
                FeatureRow(
                    icon: "folder.fill",
                    title: "Kategorien",
                    description: "Wähle eine Kategorie aus der Seitenleiste"
                )
                
                FeatureRow(
                    icon: "plus.circle.fill",
                    title: "Artikel hinzufügen",
                    description: "Erstelle deine Einkaufsliste mit Prioritäten"
                )
                
                FeatureRow(
                    icon: "chart.bar.fill",
                    title: "Statistiken",
                    description: "Verfolge deine Einkaufsgewohnheiten"
                )
            }
        }
        .padding(40)
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Add Item Sheet
struct AddItemSheet: View {
    @ObservedObject var viewModel: ShoppingListViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var name = ""
    @State private var selectedCategory: ShoppingCategory
    @State private var quantity: Int = 1
    @State private var priority: Priority = .normal
    @State private var notes = ""
    @State private var estimatedPrice = ""
    @State private var store = ""
    
    init(viewModel: ShoppingListViewModel) {
        self.viewModel = viewModel
        _selectedCategory = State(initialValue: viewModel.selectedCategory ?? .other)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Artikel Details") {
                    TextField("Artikelname", text: $name)
                        .textInputAutocapitalization(.words)
                    
                    Picker("Kategorie", selection: $selectedCategory) {
                        ForEach(ShoppingCategory.allCases, id: \.self) { category in
                            Label(category.rawValue, systemImage: category.icon)
                                .tag(category)
                        }
                    }
                    
                    Stepper("Menge: \(quantity)", value: $quantity, in: 1...99)
                }
                
                Section("Zusätzliche Informationen") {
                    Picker("Priorität", selection: $priority) {
                        ForEach(Priority.allCases, id: \.self) { priority in
                            Label(priority.rawValue, systemImage: priority.icon)
                                .foregroundColor(priority.color)
                                .tag(priority)
                        }
                    }
                    
                    TextField("Notizen (optional)", text: $notes, axis: .vertical)
                        .lineLimit(3)
                    
                    TextField("Geschäft (optional)", text: $store)
                        .textInputAutocapitalization(.words)
                    
                    TextField("Geschätzter Preis (optional)", text: $estimatedPrice)
                        .keyboardType(.decimalPad)
                }
                
                Section {
                    Button("Artikel hinzufügen") {
                        let price = Double(estimatedPrice.replacingOccurrences(of: ",", with: ".")) ?? 0
                        
                        viewModel.addItem(
                            name: name,
                            category: selectedCategory,
                            quantity: Int32(quantity),
                            priority: priority,
                            notes: notes,
                            estimatedPrice: price,
                            store: store
                        )
                        
                        dismiss()
                    }
                    .disabled(name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .navigationTitle("Neuer Artikel")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Abbrechen") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Item Detail Sheet
struct ItemDetailSheet: View {
    let item: EinkaufsItem
    @ObservedObject var viewModel: ShoppingListViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var actualPrice = ""
    @State private var notes = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section("Artikel Information") {
                    HStack {
                        Text("Name")
                        Spacer()
                        Text(item.wrappedName)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Kategorie")
                        Spacer()
                        Label(item.categoryEnum.rawValue, systemImage: item.categoryEnum.icon)
                            .foregroundColor(item.categoryEnum.color)
                    }
                    
                    HStack {
                        Text("Priorität")
                        Spacer()
                        Label(item.priorityEnum.rawValue, systemImage: item.priorityEnum.icon)
                            .foregroundColor(item.priorityEnum.color)
                    }
                    
                    if item.quantity > 1 {
                        HStack {
                            Text("Menge")
                            Spacer()
                            Text("\(item.quantity)")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                if item.isPurchased {
                    Section("Einkauf abgeschlossen") {
                        TextField("Tatsächlicher Preis", text: $actualPrice)
                            .keyboardType(.decimalPad)
                            .onAppear {
                                if item.actualPrice > 0 {
                                    actualPrice = String(format: "%.2f", item.actualPrice)
                                }
                            }
                        
                        if let dateCompleted = item.dateCompleted {
                            HStack {
                                Text("Erledigt am")
                                Spacer()
                                Text(dateCompleted, format: .dateTime.day().month().year().hour().minute())
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                if item.estimatedPrice > 0 {
                    Section("Preisvergleich") {
                        HStack {
                            Text("Geschätzt")
                            Spacer()
                            Text(String(format: "%.2f €", item.estimatedPrice))
                                .foregroundColor(.blue)
                        }
                        
                        if item.actualPrice > 0 {
                            HStack {
                                Text("Tatsächlich")
                                Spacer()
                                Text(String(format: "%.2f €", item.actualPrice))
                                    .foregroundColor(.green)
                            }
                            
                            let difference = item.estimatedPrice - item.actualPrice
                            HStack {
                                Text("Differenz")
                                Spacer()
                                Text(String(format: "%+.2f €", difference))
                                    .foregroundColor(difference >= 0 ? .green : .red)
                            }
                        }
                    }
                }
                
                if !item.wrappedNotes.isEmpty {
                    Section("Notizen") {
                        Text(item.wrappedNotes)
                            .foregroundColor(.secondary)
                    }
                }
                
                if let store = item.store, !store.isEmpty {
                    Section("Geschäft") {
                        Text(store)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Artikel Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Schließen") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Speichern") {
                        if let price = Double(actualPrice.replacingOccurrences(of: ",", with: ".")) {
                            viewModel.updateItem(item, actualPrice: price)
                        }
                        dismiss()
                    }
                    .disabled(actualPrice.isEmpty && item.isPurchased)
                }
            }
        }
    }
}

// MARK: - All Items View
struct AllItemsView: View {
    @ObservedObject var viewModel: ShoppingListViewModel
    @FetchRequest(
        sortDescriptors: [
            NSSortDescriptor(keyPath: \EinkaufsItem.isPurchased, ascending: true),
            NSSortDescriptor(keyPath: \EinkaufsItem.priority, ascending: true),
            NSSortDescriptor(keyPath: \EinkaufsItem.dateAdded, ascending: false)
        ]
    ) private var allItems: FetchedResults<EinkaufsItem>
    
    var body: some View {
        List {
            ForEach(ShoppingCategory.allCases, id: \.self) { category in
                let categoryItems = allItems.filter { $0.categoryEnum == category }
                
                if !categoryItems.isEmpty {
                    Section {
                        ForEach(categoryItems, id: \.objectID) { item in
                            ItemRowView(item: item, viewModel: viewModel)
                        }
                    } header: {
                        Label(category.rawValue, systemImage: category.icon)
                            .foregroundColor(category.color)
                    }
                }
            }
        }
        .navigationTitle("Alle Artikel")
        .navigationBarTitleDisplayMode(.large)
    }
}

struct ItemRowView: View {
    let item: EinkaufsItem
    @ObservedObject var viewModel: ShoppingListViewModel
    
    var body: some View {
        HStack {
            Button {
                viewModel.togglePurchased(item)
            } label: {
                Image(systemName: item.isPurchased ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(item.isPurchased ? .green : .gray)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(item.wrappedName)
                    .strikethrough(item.isPurchased)
                    .foregroundColor(item.isPurchased ? .secondary : .primary)
                
                HStack {
                    Image(systemName: item.priorityEnum.icon)
                        .foregroundColor(item.priorityEnum.color)
                        .font(.caption)
                    
                    if item.quantity > 1 {
                        Text("\(item.quantity)x")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if item.estimatedPrice > 0 {
                        Text(item.formattedPrice)
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }
            }
            
            Spacer()
        }
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            Button("Löschen", role: .destructive) {
                viewModel.deleteItem(item)
            }
            
            Button("Duplizieren") {
                viewModel.duplicateItem(item)
            }
            .tint(.blue)
        }
    }
}

// MARK: - Statistics View
struct StatisticsView: View {
    @ObservedObject var viewModel: ShoppingListViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Summary Cards
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 16) {
                        StatCard(
                            title: "Gesamt Artikel",
                            value: "\(viewModel.statistics.totalItems)",
                            icon: "list.bullet.circle.fill",
                            color: .blue
                        )
                        
                        StatCard(
                            title: "Erledigt",
                            value: "\(viewModel.statistics.completedItems)",
                            icon: "checkmark.circle.fill",
                            color: .green
                        )
                        
                        StatCard(
                            title: "Completion Rate",
                            value: viewModel.statistics.formattedCompletionRate,
                            icon: "percent",
                            color: .orange
                        )
                        
                        StatCard(
                            title: "Dringende Artikel",
                            value: "\(viewModel.statistics.urgentItemsCount)",
                            icon: "exclamationmark.triangle.fill",
                            color: .red
                        )
                    }
                    
                    // Cost Analysis
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Kostenanalyse")
                            .font(.title2.weight(.bold))
                        
                        VStack(spacing: 12) {
                            HStack {
                                Text("Geschätzte Kosten")
                                Spacer()
                                Text(viewModel.statistics.formattedEstimatedCost)
                                    .foregroundColor(.blue)
                            }
                            
                            HStack {
                                Text("Tatsächliche Kosten")
                                Spacer()
                                Text(viewModel.statistics.formattedActualCost)
                                    .foregroundColor(.green)
                            }
                            
                            Divider()
                            
                            HStack {
                                Text(viewModel.statistics.savings >= 0 ? "Ersparnis" : "Mehrkosten")
                                    .font(.headline)
                                Spacer()
                                Text(viewModel.statistics.formattedSavings)
                                    .font(.headline)
                                    .foregroundColor(viewModel.statistics.savings >= 0 ? .green : .red)
                            }
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                }
                .padding()
            }
            .navigationTitle("Statistiken")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Fertig") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title)
                .foregroundColor(color)
            
            VStack(spacing: 4) {
                Text(value)
                    .font(.title.weight(.bold))
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
}

// MARK: - Settings View
struct SettingsView: View {
    @ObservedObject var viewModel: ShoppingListViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            Form {
                Section("Anzeige") {
                    Toggle("Erledigte Artikel anzeigen", isOn: $viewModel.showCompleted)
                    
                    Picker("Sortierung", selection: $viewModel.sortOption) {
                        ForEach(ShoppingListViewModel.SortOption.allCases, id: \.self) { option in
                            Label(option.rawValue, systemImage: option.icon)
                                .tag(option)
                        }
                    }
                }
                
                Section("Daten") {
                    Button("Alle erledigten Artikel löschen") {
                        viewModel.clearCompleted()
                    }
                    .foregroundColor(.red)
                    .disabled(viewModel.statistics.completedItems == 0)
                }
                
                Section("App Information") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Entwickelt von")
                        Spacer()
                        Text("EinkaufsApp Pro Team")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Einstellungen")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Fertig") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}