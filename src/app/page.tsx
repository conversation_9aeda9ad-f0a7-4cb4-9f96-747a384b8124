'use client';

import Link from "next/link";
import { useState, useEffect } from "react";

export default function Home() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeService, setActiveService] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveService((prev) => (prev + 1) % 6);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-cyan-900/20"></div>
        <div className="absolute inset-0">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-blue-400 rounded-full animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 3}s`
              }}
            ></div>
          ))}
        </div>
      </div>

      {/* Navigation */}
      <nav className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? 'bg-black/90 backdrop-blur-md shadow-2xl' : 'bg-transparent'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-20">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  P&A ITService
                </h1>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#services" className="text-gray-300 hover:text-cyan-400 transition-all duration-300 hover:scale-110">
                Services
              </Link>
              <Link href="#about" className="text-gray-300 hover:text-cyan-400 transition-all duration-300 hover:scale-110">
                Über uns
              </Link>
              <Link href="#contact" className="text-gray-300 hover:text-cyan-400 transition-all duration-300 hover:scale-110">
                Kontakt
              </Link>
              <Link
                href="#contact"
                className="bg-gradient-to-r from-cyan-500 to-purple-500 text-white px-6 py-2 rounded-full font-semibold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 hover:scale-105"
              >
                Beratung
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center z-10">
          <div className="mb-8">
            <h1 className="text-5xl md:text-8xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-pulse">
                Zukunft
              </span>
              <br />
              <span className="text-white">beginnt mit</span>
              <br />
              <span className="bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                IT-Innovation
              </span>
            </h1>
          </div>

          <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
            Revolutionäre IT-Lösungen, die Ihr Unternehmen in die digitale Zukunft katapultieren.
            <span className="text-cyan-400 font-semibold">Wir machen das Unmögliche möglich.</span>
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
            <Link
              href="#contact"
              className="group bg-gradient-to-r from-cyan-500 to-purple-500 text-white px-10 py-4 rounded-full font-bold text-lg hover:shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 hover:scale-110 relative overflow-hidden"
            >
              <span className="relative z-10">🚀 Kostenlose Beratung</span>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Link>
            <Link
              href="#services"
              className="group border-2 border-cyan-400 text-cyan-400 px-10 py-4 rounded-full font-bold text-lg hover:bg-cyan-400 hover:text-black transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-cyan-400/25"
            >
              ⚡ Unsere Power-Services
            </Link>
          </div>

          {/* Floating Elements */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full opacity-20 animate-bounce"></div>
          <div className="absolute bottom-20 right-10 w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute top-1/2 left-5 w-12 h-12 bg-gradient-to-r from-pink-400 to-cyan-400 rounded-full opacity-20 animate-ping"></div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-cyan-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-cyan-400 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="relative py-32 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-7xl font-bold mb-8">
              <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                Power-Services
              </span>
            </h2>
            <p className="text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Erleben Sie IT-Services der nächsten Generation -
              <span className="text-cyan-400 font-bold"> revolutionär, kraftvoll, zukunftsweisend</span>
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🧠",
                title: "KI-Powered IT-Beratung",
                description: "Strategische Beratung mit künstlicher Intelligenz für maximale Effizienz",
                gradient: "from-purple-500 to-pink-500",
                delay: "0s"
              },
              {
                icon: "⚡",
                title: "Hyper-Speed Server",
                description: "Blitzschnelle Server-Infrastruktur mit 99.99% Uptime-Garantie",
                gradient: "from-cyan-500 to-blue-500",
                delay: "0.2s"
              },
              {
                icon: "🛡️",
                title: "Quantum-Sicherheit",
                description: "Militärgrade Sicherheit mit quantenverschlüsselten Protokollen",
                gradient: "from-green-500 to-teal-500",
                delay: "0.4s"
              },
              {
                icon: "🚀",
                title: "24/7 Turbo-Support",
                description: "Sofortiger Support mit KI-gestützter Problemlösung in Echtzeit",
                gradient: "from-orange-500 to-red-500",
                delay: "0.6s"
              },
              {
                icon: "🌐",
                title: "Next-Gen Webapps",
                description: "Futuristische Webanwendungen mit AR/VR Integration",
                gradient: "from-indigo-500 to-purple-500",
                delay: "0.8s"
              },
              {
                icon: "💎",
                title: "Diamond Data-Vault",
                description: "Unzerstörbare Datensicherung mit Blockchain-Technologie",
                gradient: "from-yellow-500 to-orange-500",
                delay: "1s"
              }
            ].map((service, index) => (
              <div
                key={index}
                className={`group relative bg-gradient-to-br ${service.gradient} p-1 rounded-2xl hover:scale-105 transition-all duration-500 hover:shadow-2xl hover:shadow-cyan-500/25`}
                style={{ animationDelay: service.delay }}
              >
                <div className="bg-black/90 backdrop-blur-sm p-8 rounded-2xl h-full relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className="relative z-10">
                    <div className="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">
                      {service.icon}
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-gray-300 text-lg leading-relaxed group-hover:text-white transition-colors duration-300">
                      {service.description}
                    </p>
                  </div>

                  {/* Animated border */}
                  <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${service.gradient} animate-pulse`}></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Interactive Service Showcase */}
          <div className="mt-32 text-center">
            <h3 className="text-4xl font-bold text-white mb-12">
              <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                Live Service Monitor
              </span>
            </h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-gradient-to-br from-green-500/20 to-teal-500/20 backdrop-blur-sm border border-green-500/30 rounded-xl p-6">
                <div className="text-3xl font-bold text-green-400 mb-2">99.99%</div>
                <div className="text-gray-300">Server Uptime</div>
                <div className="w-full bg-gray-700 rounded-full h-2 mt-4">
                  <div className="bg-gradient-to-r from-green-400 to-teal-400 h-2 rounded-full w-full animate-pulse"></div>
                </div>
              </div>
              <div className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur-sm border border-blue-500/30 rounded-xl p-6">
                <div className="text-3xl font-bold text-blue-400 mb-2">&lt;1ms</div>
                <div className="text-gray-300">Response Time</div>
                <div className="w-full bg-gray-700 rounded-full h-2 mt-4">
                  <div className="bg-gradient-to-r from-blue-400 to-cyan-400 h-2 rounded-full w-full animate-pulse"></div>
                </div>
              </div>
              <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-purple-500/30 rounded-xl p-6">
                <div className="text-3xl font-bold text-purple-400 mb-2">24/7</div>
                <div className="text-gray-300">Support Active</div>
                <div className="w-full bg-gray-700 rounded-full h-2 mt-4">
                  <div className="bg-gradient-to-r from-purple-400 to-pink-400 h-2 rounded-full w-full animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="relative py-32 bg-gradient-to-b from-gray-900 to-black overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="relative">
              <h2 className="text-5xl md:text-7xl font-bold mb-8">
                <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  Die Zukunft
                </span>
                <br />
                <span className="text-white">ist unser</span>
                <br />
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Spielfeld
                </span>
              </h2>

              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Seit über einem Jahrzehnt revolutionieren wir die IT-Landschaft.
                <span className="text-cyan-400 font-bold"> Wir sind nicht nur IT-Experten - wir sind Visionäre</span>,
                die das Unmögliche möglich machen.
              </p>

              <p className="text-lg text-gray-400 mb-12 leading-relaxed">
                Jedes Unternehmen ist einzigartig, jede Herausforderung ist eine Chance.
                Wir entwickeln nicht nur Lösungen - wir erschaffen digitale Welten,
                die Ihre Konkurrenz ins Staunen versetzen.
              </p>

              {/* Animated Stats */}
              <div className="grid grid-cols-2 gap-8 mb-12">
                <div className="group">
                  <div className="text-5xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                    500+
                  </div>
                  <div className="text-gray-300 group-hover:text-cyan-400 transition-colors duration-300">
                    Revolutionierte Unternehmen
                  </div>
                </div>
                <div className="group">
                  <div className="text-5xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                    15+
                  </div>
                  <div className="text-gray-300 group-hover:text-purple-400 transition-colors duration-300">
                    Jahre Innovation
                  </div>
                </div>
              </div>

              {/* CTA Button */}
              <Link
                href="#contact"
                className="inline-block bg-gradient-to-r from-cyan-500 to-purple-500 text-white px-8 py-4 rounded-full font-bold text-lg hover:shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 hover:scale-110"
              >
                🚀 Starten Sie Ihre Revolution
              </Link>
            </div>

            {/* Interactive Feature Cards */}
            <div className="relative">
              <div className="grid gap-6">
                {[
                  {
                    icon: "🎯",
                    title: "Präzisions-Engineering",
                    description: "Maßgeschneiderte Lösungen mit chirurgischer Präzision",
                    gradient: "from-cyan-500/20 to-blue-500/20",
                    border: "border-cyan-500/30"
                  },
                  {
                    icon: "⚡",
                    title: "Lichtgeschwindigkeit",
                    description: "Blitzschnelle Implementierung und Support",
                    gradient: "from-purple-500/20 to-pink-500/20",
                    border: "border-purple-500/30"
                  },
                  {
                    icon: "🛡️",
                    title: "Unzerstörbare Sicherheit",
                    description: "Fort Knox-Level Schutz für Ihre Daten",
                    gradient: "from-green-500/20 to-teal-500/20",
                    border: "border-green-500/30"
                  },
                  {
                    icon: "🌟",
                    title: "Zukunfts-Garantie",
                    description: "Technologien von morgen, heute verfügbar",
                    gradient: "from-yellow-500/20 to-orange-500/20",
                    border: "border-yellow-500/30"
                  }
                ].map((feature, index) => (
                  <div
                    key={index}
                    className={`group bg-gradient-to-br ${feature.gradient} backdrop-blur-sm border ${feature.border} rounded-xl p-6 hover:scale-105 transition-all duration-500 hover:shadow-xl`}
                  >
                    <div className="flex items-start space-x-4">
                      <div className="text-4xl group-hover:scale-110 transition-transform duration-300">
                        {feature.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                          {feature.title}
                        </h3>
                        <p className="text-gray-300 group-hover:text-white transition-colors duration-300">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-10 -right-10 w-20 h-20 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-10 -left-10 w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-bounce"></div>
            </div>
          </div>

          {/* Achievement Showcase */}
          <div className="mt-32 text-center">
            <h3 className="text-4xl font-bold text-white mb-16">
              <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                Unsere Superkräfte
              </span>
            </h3>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                { metric: "99.99%", label: "Uptime Garantie", icon: "⚡" },
                { metric: "<1s", label: "Response Time", icon: "🚀" },
                { metric: "24/7", label: "Support Verfügbar", icon: "🛡️" },
                { metric: "∞", label: "Möglichkeiten", icon: "🌟" }
              ].map((stat, index) => (
                <div key={index} className="group">
                  <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    {stat.icon}
                  </div>
                  <div className="text-4xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent mb-2">
                    {stat.metric}
                  </div>
                  <div className="text-gray-300 group-hover:text-white transition-colors duration-300">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Background Animation */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-500/10 to-purple-500/10 rounded-full filter blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full filter blur-3xl animate-pulse animation-delay-2000"></div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="relative py-32 bg-gradient-to-b from-black to-gray-900 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-7xl font-bold mb-8">
              <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                Bereit für die
              </span>
              <br />
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Revolution?
              </span>
            </h2>
            <p className="text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Lassen Sie uns gemeinsam Ihr Unternehmen in die digitale Zukunft katapultieren.
              <span className="text-cyan-400 font-bold">Der erste Schritt beginnt jetzt!</span>
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Info */}
            <div className="space-y-8">
              <h3 className="text-3xl font-bold text-white mb-8">
                <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  Kontakt-Zentrale
                </span>
              </h3>

              {[
                {
                  icon: "📍",
                  title: "Hauptquartier",
                  content: "Innovation District 42\nZukunftsstraße 2025\n12345 Tech City",
                  gradient: "from-cyan-500/20 to-blue-500/20",
                  border: "border-cyan-500/30"
                },
                {
                  icon: "📞",
                  title: "Hotline",
                  content: "+49 (0) 2025 FUTURE\n24/7 Sofort-Support",
                  gradient: "from-purple-500/20 to-pink-500/20",
                  border: "border-purple-500/30"
                },
                {
                  icon: "✉️",
                  title: "Digital Gateway",
                  content: "<EMAIL>\nAntwort in <1 Stunde garantiert",
                  gradient: "from-green-500/20 to-teal-500/20",
                  border: "border-green-500/30"
                },
                {
                  icon: "⏰",
                  title: "Verfügbarkeit",
                  content: "Mo-Fr: 24/7 Online\nWochenende: Notfall-Support\nFeiertage: Immer erreichbar",
                  gradient: "from-yellow-500/20 to-orange-500/20",
                  border: "border-yellow-500/30"
                }
              ].map((contact, index) => (
                <div
                  key={index}
                  className={`group bg-gradient-to-br ${contact.gradient} backdrop-blur-sm border ${contact.border} rounded-xl p-6 hover:scale-105 transition-all duration-500 hover:shadow-xl`}
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-4xl group-hover:scale-110 transition-transform duration-300">
                      {contact.icon}
                    </div>
                    <div>
                      <h4 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                        {contact.title}
                      </h4>
                      <p className="text-gray-300 whitespace-pre-line group-hover:text-white transition-colors duration-300">
                        {contact.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))}

              {/* Social Links */}
              <div className="pt-8">
                <h4 className="text-xl font-bold text-white mb-6">Folgen Sie unserer Revolution</h4>
                <div className="flex space-x-4">
                  {[
                    { platform: "LinkedIn", icon: "💼", color: "from-blue-500 to-blue-600" },
                    { platform: "Twitter", icon: "🐦", color: "from-cyan-500 to-blue-500" },
                    { platform: "GitHub", icon: "💻", color: "from-gray-500 to-gray-600" },
                    { platform: "YouTube", icon: "📺", color: "from-red-500 to-red-600" }
                  ].map((social, index) => (
                    <a
                      key={index}
                      href="#"
                      className={`w-12 h-12 bg-gradient-to-r ${social.color} rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300 hover:shadow-lg`}
                    >
                      <span className="text-xl">{social.icon}</span>
                    </a>
                  ))}
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="relative">
              <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                <h3 className="text-3xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                    🚀 Revolution starten
                  </span>
                </h3>

                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                        Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300"
                        placeholder="Ihr Name"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                        E-Mail *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                      Telefon
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300"
                      placeholder="+49 123 456789"
                    />
                  </div>

                  <div>
                    <label htmlFor="service" className="block text-sm font-medium text-gray-300 mb-2">
                      Gewünschter Service
                    </label>
                    <select
                      id="service"
                      name="service"
                      className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300"
                    >
                      <option value="">Service auswählen</option>
                      <option value="consulting">🧠 KI-Powered IT-Beratung</option>
                      <option value="server">⚡ Hyper-Speed Server</option>
                      <option value="security">🛡️ Quantum-Sicherheit</option>
                      <option value="support">🚀 24/7 Turbo-Support</option>
                      <option value="web">🌐 Next-Gen Webapps</option>
                      <option value="data">💎 Diamond Data-Vault</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                      Ihre Vision *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={4}
                      required
                      className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300"
                      placeholder="Beschreiben Sie Ihre digitale Vision..."
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-cyan-500 to-purple-500 text-white py-4 px-8 rounded-lg font-bold text-lg hover:shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 hover:scale-105 relative overflow-hidden group"
                  >
                    <span className="relative z-10">🚀 Revolution jetzt starten!</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </button>
                </form>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-6 -right-6 w-12 h-12 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-bounce"></div>
            </div>
          </div>
        </div>

        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-cyan-500/5 to-purple-500/5 rounded-full filter blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full filter blur-3xl animate-pulse animation-delay-2000"></div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gradient-to-b from-gray-900 to-black py-20 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid md:grid-cols-4 gap-12">
            {/* Company Info */}
            <div className="md:col-span-2">
              <h3 className="text-4xl font-bold mb-6">
                <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  P&A ITService
                </span>
              </h3>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Wir sind nicht nur ein IT-Unternehmen - wir sind
                <span className="text-cyan-400 font-bold"> digitale Revolutionäre</span>,
                die Unmögliches möglich machen und Zukunft gestalten.
              </p>

              {/* Social Media */}
              <div className="flex space-x-4 mb-8">
                {[
                  { platform: "LinkedIn", icon: "💼", color: "from-blue-500 to-blue-600", hover: "hover:shadow-blue-500/25" },
                  { platform: "Twitter", icon: "🐦", color: "from-cyan-500 to-blue-500", hover: "hover:shadow-cyan-500/25" },
                  { platform: "GitHub", icon: "💻", color: "from-gray-500 to-gray-600", hover: "hover:shadow-gray-500/25" },
                  { platform: "YouTube", icon: "📺", color: "from-red-500 to-red-600", hover: "hover:shadow-red-500/25" },
                  { platform: "Instagram", icon: "📸", color: "from-pink-500 to-purple-500", hover: "hover:shadow-pink-500/25" }
                ].map((social, index) => (
                  <a
                    key={index}
                    href="#"
                    className={`w-14 h-14 bg-gradient-to-r ${social.color} rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300 hover:shadow-xl ${social.hover}`}
                  >
                    <span className="text-2xl">{social.icon}</span>
                  </a>
                ))}
              </div>

              {/* Newsletter */}
              <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
                <h4 className="text-xl font-bold text-white mb-4">
                  🚀 Revolution Newsletter
                </h4>
                <p className="text-gray-300 mb-4">
                  Erhalten Sie exklusive Einblicke in die Zukunft der IT
                </p>
                <div className="flex gap-3">
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    className="flex-1 px-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  />
                  <button className="bg-gradient-to-r from-cyan-500 to-purple-500 text-white px-6 py-2 rounded-lg font-semibold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 hover:scale-105">
                    Abonnieren
                  </button>
                </div>
              </div>
            </div>

            {/* Services */}
            <div>
              <h4 className="text-2xl font-bold text-white mb-6">
                <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  Power-Services
                </span>
              </h4>
              <ul className="space-y-3">
                {[
                  { name: "🧠 KI-Beratung", href: "#" },
                  { name: "⚡ Hyper-Server", href: "#" },
                  { name: "🛡️ Quantum-Security", href: "#" },
                  { name: "🚀 Turbo-Support", href: "#" },
                  { name: "🌐 Next-Gen Web", href: "#" },
                  { name: "💎 Data-Vault", href: "#" }
                ].map((service, index) => (
                  <li key={index}>
                    <a
                      href={service.href}
                      className="text-gray-400 hover:text-cyan-400 transition-all duration-300 hover:translate-x-2 block"
                    >
                      {service.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h4 className="text-2xl font-bold text-white mb-6">
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Kontakt-Hub
                </span>
              </h4>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <span className="text-2xl">📍</span>
                  <div>
                    <div className="text-gray-300">Innovation District 42</div>
                    <div className="text-gray-400">Zukunftsstraße 2025</div>
                    <div className="text-gray-400">12345 Tech City</div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <span className="text-2xl">📞</span>
                  <div className="text-gray-300">+49 (0) 2025 FUTURE</div>
                </div>

                <div className="flex items-center space-x-3">
                  <span className="text-2xl">✉️</span>
                  <div className="text-gray-300"><EMAIL></div>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="text-2xl">⏰</span>
                  <div>
                    <div className="text-gray-300">24/7 Online</div>
                    <div className="text-gray-400">Immer für Sie da</div>
                  </div>
                </div>
              </div>

              {/* Quick Contact */}
              <div className="mt-8">
                <Link
                  href="#contact"
                  className="inline-block bg-gradient-to-r from-cyan-500 to-purple-500 text-white px-6 py-3 rounded-full font-bold hover:shadow-xl hover:shadow-cyan-500/25 transition-all duration-300 hover:scale-105"
                >
                  🚀 Sofort-Kontakt
                </Link>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-800 mt-16 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="text-gray-400 text-center md:text-left">
                <p>&copy; 2024 P&A ITService. Alle Rechte vorbehalten.</p>
                <p className="text-sm mt-1">Powered by Innovation • Secured by Quantum • Driven by Passion</p>
              </div>

              <div className="flex space-x-6 text-sm">
                <a href="#" className="text-gray-400 hover:text-cyan-400 transition-colors">Datenschutz</a>
                <a href="#" className="text-gray-400 hover:text-cyan-400 transition-colors">Impressum</a>
                <a href="#" className="text-gray-400 hover:text-cyan-400 transition-colors">AGB</a>
                <a href="#" className="text-gray-400 hover:text-cyan-400 transition-colors">Cookie-Richtlinie</a>
              </div>
            </div>

            {/* Animated Status Bar */}
            <div className="mt-8 text-center">
              <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-green-500/20 to-teal-500/20 backdrop-blur-sm border border-green-500/30 rounded-full px-6 py-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-400 font-semibold">System Status: Alle Services Online</span>
                <div className="text-green-400">99.99% Uptime</div>
              </div>
            </div>
          </div>
        </div>

        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-500/5 to-purple-500/5 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full filter blur-3xl"></div>
        </div>
      </footer>
    </div>
  );
}
